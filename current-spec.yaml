alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
ingress:
  rules:
  - component:
      name: interior-designer
    match:
      authority: {}
      path:
        prefix: /api
  - component:
      name: frontend-hello
    match:
      authority: {}
      path:
        prefix: /
name: orca-app
region: atl
services:
# Backend API service
- bitbucket:
    branch: do-deployment
    deploy_on_push: true
    repo: tonatiuhnb/interior-designer
  dockerfile_path: /Dockerfile
  http_port: 8080
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-0.5gb
  name: interior-designer
  source_dir: /
# Frontend static site service
- bitbucket:
    branch: do-deployment
    deploy_on_push: true
    repo: tonatiuhnb/interior-designer
  build_command: cd frontend && npm install && npm run build:hello
  name: frontend-hello
  source_dir: /
  static_sites:
  - name: react-static
    output_dir: frontend/dist
