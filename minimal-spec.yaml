alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
ingress:
  rules:
  - component:
      name: interior-designer
    match:
      path:
        prefix: /api
  - component:
      name: frontend-hello
    match:
      path:
        prefix: /
name: orca-app
region: atl
services:
# Backend API service
- bitbucket:
    branch: do-deployment
    repo: tonatiuhnb/interior-designer
  dockerfile_path: /Dockerfile
  http_port: 8080
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-0.5gb
  name: interior-designer
  source_dir: /
static_sites:
# Frontend static site
- bitbucket:
    branch: do-deployment
    repo: tonatiuhnb/interior-designer
  build_command: npm install && npm run build:hello
  environment_slug: node-js
  name: frontend-hello
  output_dir: dist
  source_dir: /frontend
