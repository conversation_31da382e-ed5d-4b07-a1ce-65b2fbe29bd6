<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/src/logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Web Interior Designer - Hello World</title>
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f5f5f5;
      }
      .App {
        text-align: center;
      }
      .App-header {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: calc(10px + 2vmin);
        color: #333;
      }
      .hello-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin: 2rem;
        max-width: 600px;
      }
      .api-status {
        font-size: 1rem;
        margin-top: 2rem;
        padding: 0.5rem;
        background-color: #f0f0f0;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/hello_main.tsx"></script>
  </body>
</html>