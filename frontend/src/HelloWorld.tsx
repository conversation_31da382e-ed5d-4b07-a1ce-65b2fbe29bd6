import { useState, useEffect } from 'react'
import './styles.css'

function HelloWorld() {
  // Remove unused message state for now
  const [apiStatus, setApiStatus] = useState('Checking API connection...')

  useEffect(() => {
    // Try to connect to the API
    fetch('/api/health')
      .then(response => response.json())
      .then(data => {
        setApiStatus(`API Status: ${data.status}`)
      })
      .catch(error => {
        setApiStatus(`API Error: ${error.message}`)
      })
  }, [])

  return (
    <div className="App">
      <header className="App-header">
        <h1>Web Interior Designer</h1>
        <div className="hello-card">
          <h2>Hello World!</h2>
          <p>This is a simplified version of the Web Interior Designer application.</p>
          <p>The full application will allow you to upload interior photos and redesign them using AI.</p>
          <p className="api-status">{apiStatus}</p>
        </div>
      </header>
    </div>
  )
}

export default HelloWorld