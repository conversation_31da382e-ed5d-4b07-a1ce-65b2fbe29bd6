{"name": "web-interior-designer", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "dev:dry": "VITE_DRY_MODE=true vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "build:hello": "vite build --config vite.hello.config.js", "serve": "vite preview", "test": "vitest run", "lint": "eslint", "format": "prettier", "check": "prettier --write . && eslint --fix"}, "dependencies": {"@aws-amplify/backend": "^1.16.0", "@chakra-ui/react": "^3.19.1", "@emotion/react": "^11.14.0", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-router": "^1.114.3", "@tanstack/react-router-devtools": "^1.114.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "tailwindcss": "^4.0.6"}, "devDependencies": {"@tanstack/eslint-config": "^0.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "prettier": "^3.5.3", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}