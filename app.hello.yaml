name: web-interior-designer-hello
region: nyc
services:
  - name: api-hello
    dockerfile_path: Dockerfile.hello
    git:
      repo_clone_url: https://bitbucket.org/tonatiuhnb/interior-designer.git
      branch: do-deployment
      deploy_on_push: true
    health_check:
      http_path: /api/health
    http_port: 8080
    instance_count: 1
    instance_size_slug: basic-xxs
    routes:
      - path: /api
    source_dir: /

  - name: frontend-hello
    build_command: cd frontend && npm install && npm run build:hello
    environment_slug: node-js
    git:
      repo_clone_url: https://bitbucket.org/tonatiuhnb/interior-designer.git
      branch: do-deployment
      deploy_on_push: true
    output_dir: frontend/dist
    routes:
      - path: /
    source_dir: /