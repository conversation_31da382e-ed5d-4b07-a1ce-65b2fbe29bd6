name: orca-app
region: atl
services:
  # Existing backend service
  - name: interior-designer
    bitbucket:
      branch: do-deployment
      deploy_on_push: true
      repo: tonatiuhnb/interior-designer
    dockerfile_path: /Dockerfile
    http_port: 8080
    instance_count: 1
    instance_size_slug: apps-s-1vcpu-0.5gb
    source_dir: /
    routes:
      - path: /api

  # New frontend service
  - name: frontend-hello
    build_command: cd frontend && npm install && npm run build:hello
    environment_slug: node-js
    bitbucket:
      branch: do-deployment
      deploy_on_push: true
      repo: tonatiuhnb/interior-designer
    static_sites:
      - name: react-static
        path: /frontend/dist
    source_dir: /
    routes:
      - path: /

alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
